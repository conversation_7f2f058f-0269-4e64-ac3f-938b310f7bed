import { DBOS } from '@dbos-inc/dbos-sdk';
import { QueuedMessage } from '../models/types';
import { ChatService } from '../core/ChatService';
import { ConversationService } from '../core/ConversationService';
import { MessageQueueService } from '../core/MessageQueueService';
import { MessageValidationService } from '../core/MessageValidationService';
import { PromptService } from '../core/PromptService';
import { SessionManager } from './SessionManager';
import { MessageStreamer } from './MessageStreamer';
import { logger } from '../utils/Logger';
import WebSocket from 'ws';

// ===== MESSAGE QUEUE PROCESSOR COMPONENT =====

export class MessageQueueProcessor {
  constructor(
    private sessionManager: SessionManager,
    private messageStreamer: MessageStreamer,
    private chatService: ChatService
  ) {}

  // Add a method to poll for and handle delayed character thoughts
  async pollForDelayedThoughts(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.conversationId) return;

    // Clear any existing polling interval to avoid duplicates
    if (session.pollInterval) {
      clearInterval(session.pollInterval);
    }

    // Set up polling interval (every 2 seconds for more responsive queue processing)
    const pollInterval = setInterval(async () => {
      try {
        // Only check if the session is still active
        if (!session || !session.ws || session.ws.readyState !== WebSocket.OPEN) {
          clearInterval(pollInterval);
          return;
        }

        // Skip polling if currently streaming
        if (session.isStreaming) {
          return;
        }

        // First, check for messages that need validation
        const messagesNeedingValidation = await MessageQueueService.getMessagesNeedingValidation(session.conversationId!);

        if (messagesNeedingValidation.length > 0) {
          logger.info(`=== VALIDATION QUEUE ===`);
          logger.info(`Conversation: ${session.conversationId}, Messages needing validation: ${messagesNeedingValidation.length}`);
          logger.info(`Messages: ${messagesNeedingValidation.map(m => `${m.character}:${m.id}`).join(', ')}`);

          // Validate messages (don't await - let them validate in background)
          for (const message of messagesNeedingValidation) {
            logger.info(`Starting validation for message ${message.id} from ${message.character}`);
            MessageValidationService.validateQueuedMessage(message.id).catch(error => {
              logger.error(`Error validating message ${message.id} from ${message.character}`, error);
            });
          }
        }

        // Get validated messages that are ready to be sent
        const readyMessages = await MessageQueueService.getValidatedReadyMessages(session.conversationId!);

        logger.info(`Polling for ready messages - Conversation: ${session.conversationId}, Found: ${readyMessages.length} validated messages`);

        if (readyMessages.length > 0) {
          logger.info(`Ready messages found: ${readyMessages.map(m => `${m.character}: ${m.text.substring(0, 50)}...`).join(', ')}`);

          // Mark messages as processing to prevent duplicate delivery
          for (const message of readyMessages) {
            await MessageQueueService.updateMessageStatus(message.id, 'PROCESSING');
          }

          // If multiple messages are ready simultaneously, reframe later ones
          if (readyMessages.length > 1) {
            await this.reframeQueuedMessages(sessionId, readyMessages);
          }

          // Send a notification that new thoughts are available
          this.messageStreamer.sendMessage(sessionId, {
            type: 'delayed_thoughts_available',
            count: readyMessages.length
          });

          // Stream the messages with proper cumulative delays
          await this.messageStreamer.streamQueuedMessages(sessionId, readyMessages);
        }
      } catch (error) {
        logger.error(`Error polling for ready messages`, error);
      }
    }, 2000);

    // Store the interval ID so we can clear it when the session ends
    session.pollInterval = pollInterval;
  }

  // Reframe queued messages so later ones acknowledge earlier ones
  private async reframeQueuedMessages(sessionId: string, messages: QueuedMessage[]): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.conversationId) return;

    // Sort messages by priority and scheduled time (earliest first)
    const sortedMessages = [...messages].sort((a, b) => {
      if (a.priority !== b.priority) return a.priority - b.priority;
      return new Date(a.scheduled_at!).getTime() - new Date(b.scheduled_at!).getTime();
    });

    // For messages after the first one, reframe them to acknowledge previous messages
    for (let i = 1; i < sortedMessages.length; i++) {
      const currentMessage = sortedMessages[i];
      const previousMessages = sortedMessages.slice(0, i);

      try {
        // Build context with previous messages that will appear before this one
        const precedingContext = previousMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');

        // Get recent conversation context
        const recentMessages = await ConversationService.getDelayedThoughts(session.conversationId);
        const conversationContext = recentMessages.slice(-3).map(msg => `${msg.character}: ${msg.text}`).join('\n');

        const reframingPrompt = `Based on this conversation and the messages that will appear before yours, reframe your response to acknowledge what others have said:

Conversation context:
${conversationContext}

Messages that will appear before yours:
${precedingContext}

Your original message: "${currentMessage.text}"

Reframe your message to acknowledge the preceding messages while maintaining your character's voice. Respond with JSON in this format:
{
  "reply": [{"character": "${currentMessage.character}", "text": "your reframed message", "delay": ${currentMessage.delay_ms}}],
  "skills": [],
  "theme": "reframed response"
}`;

        const systemPrompt = await PromptService.getSystemPrompt(`${currentMessage.character.toLowerCase()}_system`);
        const llmResponse = await this.chatService.getLLMService().generate(systemPrompt, reframingPrompt);

        if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
          // Update the message text in the queue
          await MessageQueueService.updateMessageText(currentMessage.id, llmResponse.reply[0].text);
          logger.info(`Reframed queued message for ${currentMessage.character}: ${llmResponse.reply[0].text.substring(0, 50)}...`);
        }
      } catch (error) {
        logger.error(`Error reframing queued message for ${currentMessage.character}`, error);
        // Keep original message if reframing fails
      }
    }
  }
}
